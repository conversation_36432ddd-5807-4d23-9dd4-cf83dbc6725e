"""
Modern async web scraping/fetching utility with AIOHTTP.

This package provides a robust, production-ready web fetching tool that demonstrates
modern Python capabilities and efficient asynchronous HTTP handling.

Features:
- Async/await syntax for concurrent requests
- Modern Python 3.11+ features (type hints, pattern matching, dataclasses)
- AIOHTTP best practices with session management and connection pooling
- Comprehensive error handling and retry logic
- Structured data models with Pydantic
- Multiple content parsing options (JSON, HTML, text, raw)
"""

from .exceptions import (
    AuthenticationError,
    ConnectionError,
    ContentError,
    FTPAuthenticationError,
    FTPConnectionError,
    FTPError,
    FTPFileNotFoundError,
    FTPPermissionError,
    FTPProtocolError,
    FTPTimeoutError,
    FTPTransferError,
    FTPVerificationError,
    HTTPError,
    NetworkError,
    NotFoundError,
    RateLimitError,
    ServerError,
    TimeoutError,
    WebFetchError,
)
from .fetcher import (
    StreamingWebFetcher,
    WebFetcher,
    analyze_headers,
    analyze_url,
    detect_content_type,
    download_file,
    fetch_url,
    fetch_urls,
    fetch_with_cache,
    is_valid_url,
    normalize_url,
)
from .models import (
    BatchFetchRequest,
    BatchFetchResult,
    CacheConfig,
    ContentType,
    FetchConfig,
    FetchRequest,
    FetchResult,
    ProgressInfo,
    RateLimitConfig,
    RequestHeaders,
    RetryStrategy,
    SessionConfig,
    StreamingConfig,
    StreamRequest,
    StreamResult,
)
from .ftp_models import (
    FTPAuthType,
    FTPBatchRequest,
    FTPBatchResult,
    FTPConfig,
    FTPConnectionInfo,
    FTPFileInfo,
    FTPMode,
    FTPProgressInfo,
    FTPRequest,
    FTPResult,
    FTPTransferMode,
    FTPVerificationMethod,
    FTPVerificationResult,
)
from .utils import RateLimiter, ResponseAnalyzer, SimpleCache, URLValidator
from .ftp_fetcher import (
    FTPFetcher,
    ftp_download_batch,
    ftp_download_file,
    ftp_get_file_info,
    ftp_list_directory,
)

__version__ = "0.1.0"
__author__ = "Web Fetch Team"
__email__ = "<EMAIL>"

__all__ = [
    # Main classes
    "WebFetcher",
    "StreamingWebFetcher",
    "FTPFetcher",

    # Convenience functions
    "fetch_url",
    "fetch_urls",
    "download_file",
    "fetch_with_cache",
    "ftp_download_file",
    "ftp_download_batch",
    "ftp_list_directory",
    "ftp_get_file_info",

    # URL utilities
    "is_valid_url",
    "normalize_url",
    "analyze_url",

    # Response utilities
    "analyze_headers",
    "detect_content_type",

    # Utility classes
    "URLValidator",
    "ResponseAnalyzer",
    "SimpleCache",
    "RateLimiter",

    # Models and configuration
    "FetchConfig",
    "FetchRequest",
    "FetchResult",
    "BatchFetchRequest",
    "BatchFetchResult",
    "RequestHeaders",
    "StreamingConfig",
    "StreamRequest",
    "StreamResult",
    "ProgressInfo",
    "CacheConfig",
    "RateLimitConfig",
    "SessionConfig",

    # Enums
    "ContentType",
    "RetryStrategy",
    "FTPMode",
    "FTPAuthType",
    "FTPTransferMode",
    "FTPVerificationMethod",

    # FTP Models
    "FTPConfig",
    "FTPRequest",
    "FTPResult",
    "FTPBatchRequest",
    "FTPBatchResult",
    "FTPFileInfo",
    "FTPProgressInfo",
    "FTPConnectionInfo",
    "FTPVerificationResult",

    # Exceptions
    "WebFetchError",
    "NetworkError",
    "TimeoutError",
    "ConnectionError",
    "HTTPError",
    "ContentError",
    "RateLimitError",
    "AuthenticationError",
    "NotFoundError",
    "ServerError",
    "FTPError",
    "FTPConnectionError",
    "FTPAuthenticationError",
    "FTPTimeoutError",
    "FTPTransferError",
    "FTPFileNotFoundError",
    "FTPPermissionError",
    "FTPVerificationError",
    "FTPProtocolError",
]
