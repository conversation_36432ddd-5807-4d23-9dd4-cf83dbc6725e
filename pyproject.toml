[project]
name = "web-fetch"
version = "0.1.0"
description = "Modern async web scraping/fetching utility with AIOHTTP"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiohttp>=3.9.0",
    "pydantic>=2.0.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "aiofiles>=23.0.0",
    "aioftp>=0.21.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "aioresponses>=0.7.4",
]

[project.scripts]
web-fetch = "web_fetch.cli:main"
