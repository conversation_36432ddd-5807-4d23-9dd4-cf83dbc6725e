# FTP File Processing Functionality

This document describes the comprehensive FTP file processing functionality added to the web-fetch library.

## 🚀 Features

### Core FTP Operations
- **Connection Management**: Automatic connection pooling with support for both active and passive FTP modes
- **Authentication**: Support for anonymous and username/password authentication
- **File Operations**: Download, list directories, and get file information
- **Protocol Support**: Both FTP and FTPS (FTP over SSL/TLS)

### Large File Optimization
- **Streaming Downloads**: Memory-efficient streaming for files larger than available memory
- **Chunked Processing**: Configurable chunk sizes to minimize memory footprint
- **Progress Tracking**: Real-time progress updates with transfer rate and ETA calculations
- **Resumable Downloads**: Automatic resume capability for interrupted transfers
- **Timeout Handling**: Comprehensive timeout management for long-running operations

### Parallel Downloads
- **Configurable Concurrency**: Control the number of simultaneous downloads
- **Connection Pooling**: Efficient reuse of FTP connections across multiple downloads
- **Rate Limiting**: Built-in rate limiting to prevent overwhelming FTP servers
- **Batch Operations**: Process multiple files in a single operation

### Resource Verification
- **File Integrity Checks**: Multiple verification methods (size, MD5, SHA256)
- **Download Validation**: Automatic verification of successful downloads
- **Retry Logic**: Exponential backoff retry mechanism for failed operations
- **Error Handling**: Comprehensive error categorization and handling

## 📦 Installation

The FTP functionality requires the `aioftp` dependency:

```bash
pip install aioftp>=0.21.0
```

Or install the complete package with all dependencies:

```bash
pip install -e .
```

## 🔧 Quick Start

### Simple File Download

```python
import asyncio
from pathlib import Path
from web_fetch import ftp_download_file

async def main():
    result = await ftp_download_file(
        "ftp://ftp.example.com/pub/file.txt",
        Path("downloads/file.txt")
    )

    if result.is_success:
        print(f"Downloaded {result.bytes_transferred} bytes")
        print(f"Transfer rate: {result.transfer_rate_mbps:.2f} MB/s")
    else:
        print(f"Download failed: {result.error}")

asyncio.run(main())
```

### Directory Listing

```python
import asyncio
from web_fetch import ftp_list_directory

async def main():
    files = await ftp_list_directory("ftp://ftp.example.com/pub/")

    for file_info in files:
        print(f"{file_info.name} - {file_info.size} bytes")
        if file_info.is_directory:
            print("  [Directory]")

asyncio.run(main())
```

### Advanced Configuration

```python
import asyncio
from pathlib import Path
from web_fetch import (
    FTPConfig, FTPFetcher, FTPMode, FTPAuthType,
    FTPVerificationMethod
)

async def main():
    # Custom configuration
    config = FTPConfig(
        mode=FTPMode.PASSIVE,
        auth_type=FTPAuthType.USER_PASS,
        username="myuser",
        password="mypass",
        max_concurrent_downloads=5,
        enable_parallel_downloads=True,
        verification_method=FTPVerificationMethod.SHA256,
        enable_resume=True,
        chunk_size=32768,  # 32KB chunks
        rate_limit_bytes_per_second=1024*1024,  # 1MB/s limit
    )

    async with FTPFetcher(config) as fetcher:
        # Download with progress tracking
        def progress_callback(progress_info):
            if progress_info.progress_percentage:
                print(f"Progress: {progress_info.progress_percentage:.1f}%")

        result = await fetcher.download_file(
            "ftp://ftp.example.com/large_file.zip",
            Path("downloads/large_file.zip"),
            progress_callback
        )

        if result.is_success:
            print("Download completed successfully!")

asyncio.run(main())
```

### Batch Downloads

```python
import asyncio
from pathlib import Path
from web_fetch import FTPRequest, ftp_download_batch

async def main():
    # Define multiple download requests
    requests = [
        FTPRequest(
            url="ftp://ftp.example.com/file1.txt",
            local_path=Path("downloads/file1.txt")
        ),
        FTPRequest(
            url="ftp://ftp.example.com/file2.txt",
            local_path=Path("downloads/file2.txt")
        ),
        FTPRequest(
            url="ftp://ftp.example.com/file3.txt",
            local_path=Path("downloads/file3.txt")
        ),
    ]

    # Progress callback for batch operations
    def batch_progress(url, progress_info):
        filename = Path(url).name
        if progress_info.progress_percentage:
            print(f"{filename}: {progress_info.progress_percentage:.1f}%")

    # Execute batch download
    result = await ftp_download_batch(
        requests,
        parallel=True,
        progress_callback=batch_progress
    )

    print(f"Batch completed: {result.successful_requests}/{result.total_requests} files")
    print(f"Total bytes transferred: {result.total_bytes_transferred}")
    print(f"Average transfer rate: {result.average_transfer_rate_mbps:.2f} MB/s")

asyncio.run(main())
```

## 🏗️ Architecture

The FTP functionality is built with a modular architecture:

### Core Components

1. **FTPFetcher**: Main class that orchestrates all FTP operations
2. **FTPConnectionPool**: Manages connection pooling and reuse
3. **FTPFileOperations**: Handles basic file operations (download, list, info)
4. **FTPStreamingDownloader**: Provides streaming downloads for large files
5. **FTPParallelDownloader**: Manages parallel and batch downloads
6. **FTPVerificationManager**: Handles file integrity verification

### Models and Configuration

- **FTPConfig**: Comprehensive configuration for all FTP operations
- **FTPRequest/FTPResult**: Request and response models
- **FTPBatchRequest/FTPBatchResult**: Batch operation models
- **FTPFileInfo**: File and directory information
- **FTPProgressInfo**: Progress tracking information

### Exception Hierarchy

- **FTPError**: Base exception for all FTP operations
- **FTPConnectionError**: Connection-related errors
- **FTPAuthenticationError**: Authentication failures
- **FTPTimeoutError**: Timeout-related errors
- **FTPTransferError**: File transfer errors
- **FTPVerificationError**: File verification failures

## ⚙️ Configuration Options

### Connection Settings
- `connection_timeout`: Connection timeout in seconds (default: 30.0)
- `data_timeout`: Data transfer timeout in seconds (default: 300.0)
- `mode`: FTP mode - ACTIVE or PASSIVE (default: PASSIVE)
- `auth_type`: Authentication type - ANONYMOUS or USER_PASS (default: ANONYMOUS)

### Performance Settings
- `max_concurrent_downloads`: Maximum simultaneous downloads (default: 3)
- `enable_parallel_downloads`: Enable parallel execution (default: False)
- `chunk_size`: Download chunk size in bytes (default: 8192)
- `rate_limit_bytes_per_second`: Rate limiting (default: None)

### Reliability Settings
- `max_retries`: Maximum retry attempts (default: 3)
- `retry_delay`: Base retry delay in seconds (default: 2.0)
- `retry_backoff_factor`: Exponential backoff factor (default: 2.0)
- `enable_resume`: Enable resumable downloads (default: True)

### Verification Settings
- `verification_method`: File verification method - NONE, SIZE, MD5, SHA256 (default: SIZE)

## 🧪 Testing

Run the FTP tests:

```bash
# Run all FTP tests
pytest tests/test_ftp.py -v

# Run with coverage
pytest tests/test_ftp.py --cov=web_fetch.ftp_* --cov-report=html
```

## 📝 Examples

See `examples/ftp_examples.py` for comprehensive usage examples including:

- Basic file operations
- Progress tracking
- Batch downloads
- Streaming for large files
- Error handling
- Configuration options

## 🔒 Security Considerations

- Use FTPS (FTP over SSL/TLS) for secure transfers
- Store credentials securely, avoid hardcoding passwords
- Implement proper timeout values to prevent hanging connections
- Use rate limiting to be respectful to FTP servers
- Validate file integrity using checksums when possible

## 🚨 Error Handling

The FTP functionality provides comprehensive error handling:

```python
from web_fetch import (
    FTPError, FTPConnectionError, FTPAuthenticationError,
    FTPTimeoutError, FTPFileNotFoundError
)

try:
    result = await ftp_download_file(url, local_path)
except FTPAuthenticationError:
    print("Authentication failed - check credentials")
except FTPFileNotFoundError:
    print("File not found on server")
except FTPConnectionError:
    print("Connection failed - check server and network")
except FTPTimeoutError:
    print("Operation timed out")
except FTPError as e:
    print(f"FTP error: {e}")
```

## 📊 Performance Tips

1. **Use connection pooling**: Keep connections alive for multiple operations
2. **Configure appropriate chunk sizes**: Larger chunks for faster networks
3. **Enable parallel downloads**: For multiple small files
4. **Use streaming**: For files larger than available memory
5. **Implement rate limiting**: To avoid overwhelming servers
6. **Enable resume**: For unreliable connections

## 🤝 Contributing

When contributing to the FTP functionality:

1. Follow the existing code structure and patterns
2. Add comprehensive tests for new features
3. Update documentation and examples
4. Ensure backward compatibility
5. Test with real FTP servers when possible

## 📄 License

This FTP functionality is part of the web-fetch library and follows the same license terms.